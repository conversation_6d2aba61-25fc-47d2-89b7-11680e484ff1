import React, { useState, useEffect, useLayoutEffect, useRef, useCallback } from 'react';
import {
  Box,
  Typography,
  Button,
  Slider,
  CircularProgress,
  Alert,
  Grid,
  Paper,
  Tooltip,
  IconButton,
  Snackbar,
  LinearProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle
} from '@mui/material';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import InfoIcon from '@mui/icons-material/Info';
import CircleIcon from '@mui/icons-material/Circle';

import videoStore from '../services/videoStorage';
import { testAWSConnection } from '../services/awsStorage';

import Webcam from 'react-webcam';
import WebcamPermissionHandler from './WebcamPermissionHandler';
import * as faceLandmarksDetection from '@tensorflow-models/face-landmarks-detection';
import * as tf from '@tensorflow/tfjs';
import { createMetadata, updateMetadataManifest, getDeviceInfo, updateCameraResolution } from '../services/metadataService';
import { createLipNetVideo, createOvalCroppedVideo, extractVideoThumbnail } from '../services/videoProcessor';
import { handleError, ERROR_SEVERITY } from '../utils/errorHandler';

// Constants
const VIDEO_CONSTRAINTS = {
  width: 640,
  height: 480,
  frameRate: 30,
  facingMode: 'user'
};

// Region of interest for mouth detection (in pixels)
const MOUTH_ROI = {
  width: 100,
  height: 50
};

// LipNet video processing options
const LIPNET_OPTIONS = {
  targetWidth: 100,
  targetHeight: 50,
  frameRate: 25,
  greyscale: true
};

// Real-time LipNet preprocessing options
const REALTIME_LIPNET_OPTIONS = {
  targetWidth: 150,
  targetHeight: 75,
  frameRate: 25,
  greyscale: false // Temporarily disabled to focus on mouth cropping
};

const VideoRecorder = ({
  onRecordingComplete,
  disabled = false,
  category,
  phrase,
  recordingNumber,
  demographics
}) => {

  // Refs
  const webcamRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const canvasRef = useRef(null);
  const detectionRef = useRef(null);
  const deviceInfoRef = useRef(getDeviceInfo());

  // Background processing refs for dual video output
  const lipnetCanvasRef = useRef(null);
  const lipnetMediaRecorderRef = useRef(null);
  const frameProcessingRef = useRef(null);
  
  // State
  const [permissionGranted, setPermissionGranted] = useState(false);
  const [cameraError, setCameraError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [recordedChunks, setRecordedChunks] = useState([]);
  const [lipnetRecordedChunks, setLipnetRecordedChunks] = useState([]);
  const [processing, setProcessing] = useState(false);
  const [processingPhase, setProcessingPhase] = useState(''); // Track specific processing phase
  const [uploadProgress, setUploadProgress] = useState(0); // Track upload progress if available
  const [selectedCamera, setSelectedCamera] = useState('');
  const [availableCameras, setAvailableCameras] = useState([]);
  const [zoomLevel, setZoomLevel] = useState(1); // Default zoom at 1.0x
  const [canReRecord, setCanReRecord] = useState(false);
  // Removed recordingCount state - using parent data directly
  const [showSavedNotification, setShowSavedNotification] = useState(false);

  // Removed early exit functionality
  const [faceDetected, setFaceDetected] = useState(false);
  const [recordingTimer, setRecordingTimer] = useState(0);
  const [mouthPosition, setMouthPosition] = useState(null);
  const [mouthTrackingQuality, setMouthTrackingQuality] = useState(0);
  const [previousMouthPosition, setPreviousMouthPosition] = useState(null);
  const [landmarkConfidence, setLandmarkConfidence] = useState(0);



  const [lipnetQualityMetrics, setLipnetQualityMetrics] = useState({
    frameCount: 0,
    avgConfidence: 0,
    codecUsed: '',
    processingErrors: 0
  });
  const [lipnetProcessingActive, setLipnetProcessingActive] = useState(false);
  const [model, setModel] = useState(null);


  // Removed complex synchronization logic - using simple parent data flow

  // Device detection
  const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
  const isLaptop = window.innerWidth > 768;

  // Initialize background canvas for LipNet preprocessing
  const initializeLipNetCanvas = useCallback(() => {
    if (!lipnetCanvasRef.current) {
      const canvas = document.createElement('canvas');
      canvas.width = REALTIME_LIPNET_OPTIONS.targetWidth;
      canvas.height = REALTIME_LIPNET_OPTIONS.targetHeight;
      canvas.style.display = 'none'; // Hidden from UI
      lipnetCanvasRef.current = canvas;

      // Append to document body but keep hidden
      document.body.appendChild(canvas);

      console.log('🎨 Background LipNet canvas initialized:', {
        width: canvas.width,
        height: canvas.height,
        hidden: true
      });
    }
    return lipnetCanvasRef.current;
  }, []);

  // Clean up background canvas
  const cleanupLipNetCanvas = useCallback(() => {
    if (lipnetCanvasRef.current && lipnetCanvasRef.current.parentNode) {
      lipnetCanvasRef.current.parentNode.removeChild(lipnetCanvasRef.current);
      lipnetCanvasRef.current = null;
      console.log('🧹 Background LipNet canvas cleaned up');
    }
  }, []);

  // Real-time frame processing for LipNet preprocessing with quality control
  const processFrameForLipNet = useCallback(() => {
    if (!webcamRef.current || !webcamRef.current.video || !lipnetCanvasRef.current || !lipnetProcessingActive) {
      return;
    }



    const video = webcamRef.current.video;
    const canvas = lipnetCanvasRef.current;
    const ctx = canvas.getContext('2d');

    // Check if video is ready
    if (video.videoWidth === 0 || video.videoHeight === 0) {
      frameProcessingRef.current = requestAnimationFrame(processFrameForLipNet);
      return;
    }

    // Update frame count for quality metrics
    setLipnetQualityMetrics(prev => ({
      ...prev,
      frameCount: prev.frameCount + 1,
      avgConfidence: (prev.avgConfidence * prev.frameCount + landmarkConfidence) / (prev.frameCount + 1)
    }));

    try {
      // Enhanced mouth ROI calculation with auto-centering and fallback
      let cropArea;
      if (mouthPosition && mouthPosition.confidence > 0.7) {
        // Use enhanced mouth position with auto-centering
        cropArea = {
          x: mouthPosition.x,
          y: mouthPosition.y,
          width: mouthPosition.width,
          height: mouthPosition.height
        };
        console.log('🎯 High confidence crop area:', cropArea);
      } else if (mouthPosition && mouthPosition.confidence > 0.3) {
        // Low confidence - use basic mouth position with extra padding
        const padding = 30;
        cropArea = {
          x: Math.max(0, mouthPosition.x - padding),
          y: Math.max(0, mouthPosition.y - padding),
          width: Math.min(video.videoWidth - (mouthPosition.x - padding), mouthPosition.width + 2 * padding),
          height: Math.min(video.videoHeight - (mouthPosition.y - padding), mouthPosition.height + 2 * padding)
        };
        console.log('🎯 Low confidence crop area:', cropArea);
      } else {
        // Fallback to center crop focused on lower face area with 2:1 aspect ratio
        const targetAspectRatio = 150 / 75; // 2:1 for LipNet
        const fallbackWidth = 160;
        const fallbackHeight = fallbackWidth / targetAspectRatio; // 80px
        cropArea = {
          x: (video.videoWidth - fallbackWidth) / 2,
          y: (video.videoHeight - fallbackHeight) / 2 + 40, // Offset down for mouth area
          width: fallbackWidth,
          height: fallbackHeight
        };
        console.log('🎯 Fallback crop area:', cropArea);
      }

      // Validate crop area bounds
      cropArea.x = Math.max(0, Math.min(cropArea.x, video.videoWidth - cropArea.width));
      cropArea.y = Math.max(0, Math.min(cropArea.y, video.videoHeight - cropArea.height));
      cropArea.width = Math.min(cropArea.width, video.videoWidth - cropArea.x);
      cropArea.height = Math.min(cropArea.height, video.videoHeight - cropArea.y);

      console.log('🎯 Final validated crop area:', {
        x: cropArea.x.toFixed(1),
        y: cropArea.y.toFixed(1),
        width: cropArea.width.toFixed(1),
        height: cropArea.height.toFixed(1),
        aspectRatio: (cropArea.width / cropArea.height).toFixed(2)
      });

      // Clear canvas with black background
      ctx.fillStyle = 'black';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Draw the cropped mouth region to exactly 150x75 pixels
      ctx.drawImage(
        video,
        cropArea.x, cropArea.y, cropArea.width, cropArea.height,
        0, 0, REALTIME_LIPNET_OPTIONS.targetWidth, REALTIME_LIPNET_OPTIONS.targetHeight
      );

      console.log('🎯 Drew mouth ROI to canvas:', {
        sourceArea: `${cropArea.x.toFixed(1)},${cropArea.y.toFixed(1)} ${cropArea.width.toFixed(1)}x${cropArea.height.toFixed(1)}`,
        targetSize: `${REALTIME_LIPNET_OPTIONS.targetWidth}x${REALTIME_LIPNET_OPTIONS.targetHeight}`
      });

      // Apply grayscale conversion if enabled (temporarily disabled for debugging)
      if (REALTIME_LIPNET_OPTIONS.greyscale) {
        // Apply grayscale conversion using pixel manipulation for reliability
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;

        for (let i = 0; i < data.length; i += 4) {
          // Use standard luminance formula for grayscale conversion
          const gray = Math.round(data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114);
          data[i] = gray;     // Red
          data[i + 1] = gray; // Green
          data[i + 2] = gray; // Blue
          // Alpha channel (data[i + 3]) remains unchanged
        }

        ctx.putImageData(imageData, 0, 0);
        console.log('🎨 Applied grayscale conversion to mouth ROI');
      }

      // Verify final canvas dimensions
      console.log('🎯 Final canvas verification:', {
        canvasSize: `${canvas.width}x${canvas.height}`,
        expectedSize: `${REALTIME_LIPNET_OPTIONS.targetWidth}x${REALTIME_LIPNET_OPTIONS.targetHeight}`,
        matches: canvas.width === REALTIME_LIPNET_OPTIONS.targetWidth && canvas.height === REALTIME_LIPNET_OPTIONS.targetHeight
      });

    } catch (error) {
      console.warn('Frame processing error:', error);
      setLipnetQualityMetrics(prev => ({
        ...prev,
        processingErrors: prev.processingErrors + 1
      }));
    }

    // Frame-by-frame validation logging (every 25 frames to avoid spam)
    if (lipnetQualityMetrics.frameCount % 25 === 0) {
      console.log(`🎯 Frame validation: ${canvas.width}×${canvas.height}, target: ${REALTIME_LIPNET_OPTIONS.targetWidth}×${REALTIME_LIPNET_OPTIONS.targetHeight}`);
      if (landmarkConfidence < 0.5) {
        console.warn(`🎯 Low landmark confidence warning: ${landmarkConfidence.toFixed(2)}`);
      }

      // Debug: Check if canvas has content
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const hasContent = imageData.data.some(pixel => pixel > 0);
      console.log(`🎯 Canvas content check: ${hasContent ? 'HAS CONTENT' : 'EMPTY'}`);

      // Debug: Sample pixel values to verify content
      if (hasContent) {
        const centerPixelIndex = (Math.floor(canvas.height / 2) * canvas.width + Math.floor(canvas.width / 2)) * 4;
        const centerPixel = {
          r: imageData.data[centerPixelIndex],
          g: imageData.data[centerPixelIndex + 1],
          b: imageData.data[centerPixelIndex + 2]
        };
        console.log(`🎯 Center pixel RGB: ${centerPixel.r}, ${centerPixel.g}, ${centerPixel.b}`);
      }
    }

    // Continue processing if LipNet processing is active
    if (lipnetProcessingActive) {
      frameProcessingRef.current = requestAnimationFrame(processFrameForLipNet);
    }
  }, [lipnetProcessingActive, mouthPosition, landmarkConfidence, lipnetQualityMetrics.frameCount]);

  // Backend upload mode detection - check if using backend upload instead of direct S3
  const isBackendUploadMode = !process.env.REACT_APP_AWS_IDENTITY_POOL_ID ||
                              process.env.REACT_APP_AWS_IDENTITY_POOL_ID === 'your-identity-pool-id-here';

  // Only show development mode if backend URL is also not configured
  const isDevelopmentMode = isBackendUploadMode && !process.env.REACT_APP_BACKEND_URL;
  
  // Instructions for optimal lip/mouth positioning
  const lipPositioningInstructions = [
    'Position your face in the centre of the frame',
    'Ensure your mouth is clearly visible with good lighting',
    'Keep your head still while recording',
    'Speak clearly and at a normal pace',
    'Make sure your chin and lips are fully visible'
  ];
  

  


  // Handle recording complete with dual video support
  const handleRecordingComplete = useCallback(async (originalVideoBlob, lipnetVideoBlob = null) => {
    console.log('=== RECORDING COMPLETION PROCESS STARTED ===');
    console.log('🔄 Processing phase: Video processing and upload preparation');

    // Ensure processing state is active (should already be set by handleStopRecording)
    setProcessing(true);
    setProcessingPhase('processing');

    console.log('🎬 Original video blob details:', {
      size: originalVideoBlob.size,
      type: originalVideoBlob.type,
      phrase,
      category,
      recordingNumber
    });
    if (lipnetVideoBlob) {
      console.log('🎨 LipNet video blob details:', {
        size: lipnetVideoBlob.size,
        type: lipnetVideoBlob.type
      });
    } else {
      console.log('🎨 No LipNet video blob - using original only');
    }
    console.log('🔧 Environment check:', {
      backendUrl: process.env.REACT_APP_BACKEND_URL,
      hasVideoStore: !!videoStore,
      hasDemographics: !!demographics
    });

    // Parent component will handle recording count updates





    setProcessing(true);
    setIsRecording(false);

    try {
      console.log('Step 1: Creating metadata...');
      const metadata = await createMetadata({
        videoBlob: originalVideoBlob,
        phrase,
        category,
        recordingNumber,
        demographics,
        deviceInfo: deviceInfoRef.current,
        qualityMetrics: { brightness: 0, sharpness: 0, overall: 'good' }, // Simplified quality metrics
        mouthPosition,
        mouthTrackingQuality,
        landmarkConfidence, // Enhanced metadata for LipNet
        zoomLevel,
        timestamp: new Date().toISOString()
      });
      console.log('Metadata created successfully:', metadata);

      console.log('Step 3: Saving recording(s) to storage...');
      console.log('🔄 About to call videoStore with:', {
        originalVideoBlobSize: originalVideoBlob.size,
        lipnetVideoBlobSize: lipnetVideoBlob ? lipnetVideoBlob.size : 'N/A',
        metadataKeys: Object.keys(metadata),
        videoStoreExists: !!videoStore
      });

      // Update processing phase to uploading
      console.log('🔄 Processing phase: Starting AWS S3 upload');
      setProcessingPhase('uploading');
      setUploadProgress(10); // Initial upload progress

      let savedData;
      if (lipnetVideoBlob) {
        // Use dual video upload if we have both videos
        console.log('📤 Uploading dual videos to AWS S3...');
        savedData = await videoStore.saveDualRecording(originalVideoBlob, lipnetVideoBlob, metadata);
        console.log('✅ Dual recordings saved successfully:', savedData);
      } else {
        // Fallback to single video upload
        console.log('📤 Uploading single video to AWS S3...');
        savedData = await videoStore.saveRecording(originalVideoBlob, metadata);
        console.log('✅ Single recording saved successfully:', savedData);
      }

      // Update upload progress
      setUploadProgress(80);

      console.log('Step 4: Updating metadata manifest...');
      await updateMetadataManifest(metadata);
      console.log('Metadata manifest updated successfully');

      // Complete upload progress
      setUploadProgress(100);

      console.log('Step 5: Updating UI state...');
      setCanReRecord(true);
      setShowSavedNotification(true);

      console.log('Step 6: Calling parent callback...');
      console.log('  onRecordingComplete function exists:', !!onRecordingComplete);
      console.log('  savedData:', savedData);
      console.log('  metadata:', metadata);
      console.log('  qualityCheck:', qualityCheck);

      if (onRecordingComplete) {
        console.log('  🚀 Calling onRecordingComplete...');
        console.log('  📊 VideoRecorder calling parent with data:');
        console.log('    savedData:', savedData);
        console.log('    metadata:', metadata);
        console.log('    qualityCheck:', qualityCheck);
        console.log('    phrase:', phrase);
        console.log('    category:', category);
        console.log('    recordingNumber:', recordingNumber);
        onRecordingComplete(savedData, metadata, qualityCheck);
        console.log('  ✅ onRecordingComplete called successfully');
      } else {
        console.error('  ❌ onRecordingComplete function is missing!');
      }

      console.log('Step 7: Recording completion - parent will handle count updates...');
      console.log('  Parent callback will trigger sync via recordingNumber prop');
      // Don't increment local count here - let parent handle it and sync via props

      console.log('✅ Upload completed successfully - resetting processing state');
      // Reset processing state after successful completion
      setProcessing(false);
      setProcessingPhase('');
      setUploadProgress(0);

      console.log('✅ RECORDING COMPLETION PROCESS FINISHED SUCCESSFULLY ===');
      console.log('🎉 Recording saved and processed without errors!');
    } catch (error) {
      console.error('=== ERROR DURING RECORDING COMPLETION ===');
      console.error('Error details:', error);
      console.error('Error stack:', error.stack);

      // Enhanced error message handling with specific error types
      let errorMessage = 'Recording processing failed';
      let isDevModeError = false;

      if (error.message) {
        // Check for simulation mode (development)
        if (error.message.includes('AWS credentials not configured') ||
            error.message.includes('your-identity-pool-id-here') ||
            error.message.includes('simulated')) {
          errorMessage = '✅ Recording completed successfully! (Development mode - AWS not configured)';
          isDevModeError = true;
          console.log('💡 AWS not configured - this is expected in development mode');
          console.log('🎉 Recording processed successfully in simulation mode!');

          // In development mode, treat this as success
          // Don't increment local count here - let parent handle it via callback
          setCanReRecord(true);
          setShowSavedNotification(true);

          if (onRecordingComplete) {
            const mockSavedData = {
              id: `dev-recording-${Date.now()}`,
              url: 'https://simulated-upload/dev-recording.mp4',
              key: 'simulated/dev-recording.mp4',
              filename: 'dev-recording.mp4',
              phrase,
              category,
              timestamp: new Date().toISOString(),
              simulated: true
            };
            // Include recordingNumber in mock metadata to match expected format
            const mockMetadata = {
              phrase,
              category,
              recordingNumber,
              timestamp: new Date().toISOString(),
              simulated: true
            };
            console.log('  🎭 Development mode: calling onRecordingComplete with mock data');
            console.log('    mockSavedData:', mockSavedData);
            console.log('    mockMetadata:', mockMetadata);
            onRecordingComplete(mockSavedData, mockMetadata, { metrics: { overall: 'good' } });
          }

          // Don't set this as an error in dev mode
          setErrorMessage('');
          return;
        }

        // Handle specific AWS error types
        else if (error.message.includes('AWS_CREDENTIALS_ERROR')) {
          errorMessage = '🔑 AWS credentials not configured properly. Please check your environment variables and refresh the page.';
          console.error('💡 SOLUTION: Add AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY to your .env file');
        } else if (error.message.includes('AWS_ACCESS_DENIED')) {
          errorMessage = '🚫 Access denied to S3 bucket. Please check bucket permissions and IAM policies.';
          console.error('💡 SOLUTION: Verify your AWS user has s3:PutObject permissions for the bucket');
        } else if (error.message.includes('AWS_BUCKET_NOT_FOUND')) {
          errorMessage = '📁 S3 bucket does not exist. Please check the bucket name in your configuration.';
          console.error('💡 SOLUTION: Verify the bucket name in AWS_S3_BUCKET environment variable');
        } else if (error.message.includes('AWS_NETWORK_ERROR')) {
          errorMessage = '🌐 Network error during upload. Please check your internet connection and try again.';
          console.error('💡 SOLUTION: Check your internet connection and AWS service status');
        } else if (error.message.includes('AWS_FILE_TOO_LARGE')) {
          errorMessage = '📏 Recording file is too large for upload. Please try recording again.';
          console.error('💡 SOLUTION: Try recording a shorter video or check S3 upload limits');
        } else if (error.message.includes('VALIDATION_ERROR')) {
          errorMessage = '⚠️ Recording validation failed. Please try recording again.';
          console.error('💡 SOLUTION: Ensure all required fields are provided');
        } else if (error.message.includes('Backend upload failed')) {
          errorMessage = '🔄 Backend upload failed. Please check server logs for details.';
          console.error('💡 SOLUTION: Check server console for detailed error information');
        }

        // Handle general error types
        else if (error.message.includes('Access denied')) {
          errorMessage = '🚫 AWS upload failed: Access denied. Please check S3 bucket permissions.';
        } else if (error.message.includes('NoSuchBucket')) {
          errorMessage = '📁 AWS upload failed: S3 bucket does not exist.';
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          errorMessage = '🌐 Network error during upload. Please check your connection and try again.';
        } else if (error.message.includes('413') || error.message.includes('too large')) {
          errorMessage = '📏 Recording file is too large. Please try again.';
        } else if (error.message.includes('metadata')) {
          errorMessage = '📋 Error creating recording metadata. Please try again.';
        } else if (error.message.includes('storage') || error.message.includes('S3')) {
          errorMessage = '💾 Error saving recording to storage. Please try again.';
        } else {
          errorMessage = `❌ Recording processing failed: ${error.message}`;
        }
      }

      // Only set error message if this is not a development mode "error"
      if (!isDevModeError) {
        console.log('❌ Upload failed - resetting processing state for retry');
        setErrorMessage(errorMessage);
      }
    } finally {
      // Always reset processing state in finally block to ensure button re-enablement
      console.log('🔄 Resetting processing state (finally block)');
      setProcessing(false);
      setProcessingPhase('');
      setUploadProgress(0);
    }
  }, [phrase, category, recordingNumber, demographics, mouthPosition, mouthTrackingQuality, zoomLevel, onRecordingComplete, validateVideoQuality]);

  // Handle stopping recording
  const handleStopRecording = useCallback(async () => {
    if (mediaRecorderRef.current && isRecording) {
      console.log('🛑 Stopping recording and entering processing state');
      mediaRecorderRef.current.stop();
      setIsRecording(false);

      // Immediately set processing state to prevent concurrent recordings
      setProcessing(true);
      setProcessingPhase('processing');
      setUploadProgress(0);
      console.log('🔄 Processing state activated - buttons disabled');
    }
  }, [isRecording]);

  // Removed early exit handlers

  // Check camera permissions
  const checkCameraPermissions = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      const devices = await navigator.mediaDevices.enumerateDevices();
      const videoDevices = devices.filter(device => device.kind === 'videoinput');
      
      setAvailableCameras(videoDevices);
      if (videoDevices.length > 0 && !selectedCamera) {
        setSelectedCamera(videoDevices[0].deviceId);
      }
      
      stream.getTracks().forEach(track => track.stop());
      setPermissionGranted(true);
      setCameraError(false);
      setErrorMessage('');
    } catch (err) {
      const errorInfo = handleError(err, 'Camera');
      setCameraError(true);
      setPermissionGranted(false);
      setErrorMessage(errorInfo.message);
    }
  }, [selectedCamera]);
  
  // Video constraints for the webcam
  const getVideoConstraints = useCallback(() => {
    return {
      ...VIDEO_CONSTRAINTS,
      deviceId: selectedCamera ? { exact: selectedCamera } : undefined,
    };
  }, [selectedCamera]);
  
  // Handle camera change
  const handleCameraChange = useCallback((event) => {
    setSelectedCamera(event.target.value);
  }, []);
  


  // Run face detection and mouth tracking
  const detectFace = useCallback(async () => {
    if (!webcamRef.current || !webcamRef.current.video) {
      detectionRef.current = requestAnimationFrame(detectFace);
      return;
    }

    // If no model is available, skip face detection but continue other quality checks
    if (!model) {
      setFaceDetected(false);
      setMouthPosition(null);
      setMouthTrackingQuality(0);

      detectionRef.current = requestAnimationFrame(detectFace);
      return;
    }

    try {
      const video = webcamRef.current.video;

      // Debug: Log that face detection is running (occasionally to avoid spam)
      if (Math.random() < 0.01) { // Log 1% of the time
        console.log('🎯 Face detection loop running, video size:', video.videoWidth, 'x', video.videoHeight, 'model available:', !!model);
      }

      // Check if video dimensions are valid before face detection
      if (video.videoWidth === 0 || video.videoHeight === 0) {
        console.warn('Video dimensions not available for face detection');
        setFaceDetected(false);
        setMouthPosition(null);
        setMouthTrackingQuality(0);
        return;
      }

      const faces = await model.estimateFaces(video, false);



      if (faces.length > 0) {
        const face = faces[0];
        setFaceDetected(true);

        // Enhanced mouth landmarks extraction with precise lip coordinates
        const keypoints = face.keypoints;
        if (keypoints && keypoints.length > 0) {
          // MediaPipe FaceMesh lip landmark indices (empirically tested)
          // Based on MediaPipe FaceMesh 468 landmarks documentation
          const outerLipIndices = [
            61, 146, 91, 181, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318
          ];
          const innerLipIndices = [
            78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308
          ];
          const mouthCornerIndices = [61, 291]; // Left and right mouth corners

          const allLipIndices = [...new Set([...outerLipIndices, ...innerLipIndices, ...mouthCornerIndices])];
          console.log('🎯 Testing lip landmark indices:', allLipIndices);

          const lipPoints = keypoints.filter((_, index) => allLipIndices.includes(index));



          if (lipPoints.length > 0) {
            // Calculate precise mouth bounding box
            const xs = lipPoints.map(p => p.x);
            const ys = lipPoints.map(p => p.y);

            const minX = Math.min(...xs);
            const maxX = Math.max(...xs);
            const minY = Math.min(...ys);
            const maxY = Math.max(...ys);

            const mouthWidth = maxX - minX;
            const mouthHeight = maxY - minY;
            const mouthCenterX = (minX + maxX) / 2;
            const mouthCenterY = (minY + maxY) / 2;

            console.log('🎯 Mouth detection:', {
              center: { x: mouthCenterX.toFixed(1), y: mouthCenterY.toFixed(1) },
              size: { width: mouthWidth.toFixed(1), height: mouthHeight.toFixed(1) },
              bounds: { minX: minX.toFixed(1), maxX: maxX.toFixed(1), minY: minY.toFixed(1), maxY: maxY.toFixed(1) }
            });

            // Calculate landmark confidence based on detection consistency
            const confidence = Math.min(1, lipPoints.length / allLipIndices.length);
            setLandmarkConfidence(confidence);



            // Enhanced padding calculation for optimal LipNet crop (150x75 target)
            // Calculate padding to achieve target aspect ratio (2:1 width:height)
            const targetAspectRatio = 150 / 75; // 2:1
            const currentAspectRatio = mouthWidth / mouthHeight;

            let finalCropWidth, finalCropHeight;

            if (currentAspectRatio > targetAspectRatio) {
              // Mouth is too wide, height should determine final size
              finalCropHeight = Math.max(mouthHeight * 2.5, 60); // Minimum 60px height
              finalCropWidth = finalCropHeight * targetAspectRatio;
            } else {
              // Mouth is too tall, width should determine final size
              finalCropWidth = Math.max(mouthWidth * 2.0, 120); // Minimum 120px width
              finalCropHeight = finalCropWidth / targetAspectRatio;
            }

            console.log('🎯 Crop dimensions calculated:', {
              finalWidth: finalCropWidth.toFixed(1),
              finalHeight: finalCropHeight.toFixed(1),
              aspectRatio: (finalCropWidth / finalCropHeight).toFixed(2),
              targetAspectRatio: targetAspectRatio.toFixed(2)
            });

            let newMouthRegion = {
              x: Math.max(0, mouthCenterX - finalCropWidth / 2),
              y: Math.max(0, mouthCenterY - finalCropHeight / 2),
              width: Math.min(video.videoWidth - Math.max(0, mouthCenterX - finalCropWidth / 2), finalCropWidth),
              height: Math.min(video.videoHeight - Math.max(0, mouthCenterY - finalCropHeight / 2), finalCropHeight),
              centerX: mouthCenterX,
              centerY: mouthCenterY,
              confidence: confidence
            };

            // Implement smooth transitions to avoid jittery movement
            if (previousMouthPosition && confidence > 0.7) {
              const smoothingFactor = 0.7; // Higher = smoother but slower response
              newMouthRegion = {
                x: previousMouthPosition.x * smoothingFactor + newMouthRegion.x * (1 - smoothingFactor),
                y: previousMouthPosition.y * smoothingFactor + newMouthRegion.y * (1 - smoothingFactor),
                width: previousMouthPosition.width * smoothingFactor + newMouthRegion.width * (1 - smoothingFactor),
                height: previousMouthPosition.height * smoothingFactor + newMouthRegion.height * (1 - smoothingFactor),
                centerX: newMouthRegion.centerX,
                centerY: newMouthRegion.centerY,
                confidence: confidence
              };
            }

            setPreviousMouthPosition(newMouthRegion);
            setMouthPosition(newMouthRegion);

            // Enhanced quality calculation with confidence weighting
            const sizeQuality = Math.min(1, (mouthWidth * mouthHeight) / (MOUTH_ROI.width * MOUTH_ROI.height));
            const overallQuality = (sizeQuality * 0.7) + (confidence * 0.3);
            setMouthTrackingQuality(overallQuality);

            // Log tracking confidence for debugging
            if (isRecording) {
              console.log(`🎨 Mouth tracking confidence: ${confidence.toFixed(2)}`);
            }
          }
        }
      } else {
        setFaceDetected(false);
        setMouthPosition(null);
        setMouthTrackingQuality(0);
        setLandmarkConfidence(0);
        setPreviousMouthPosition(null);
      }
    } catch (error) {
      console.warn('Face detection error:', error);
      setFaceDetected(false);
      setMouthPosition(null);
      setMouthTrackingQuality(0);
      setLandmarkConfidence(0);
      setPreviousMouthPosition(null);
    }



    // Continue detection loop
    detectionRef.current = requestAnimationFrame(detectFace);
  }, [model]);

  // Handle starting recording
  const handleStartRecording = useCallback(async () => {
    try {
      // Prevent concurrent recording attempts
      if (processing || isRecording) {
        console.log('🚫 Recording attempt blocked - already processing or recording');
        console.log('  processing:', processing, 'isRecording:', isRecording);
        return;
      }

      console.log('🎬 Starting new recording session');
      // Clear any previous error messages
      setErrorMessage('');

      // Validate camera availability
      if (!webcamRef.current || !webcamRef.current.stream) {
        setErrorMessage('Camera not available. Please check camera permissions.');
        return;
      }

      // Validate video stream is active
      const stream = webcamRef.current.stream;
      const videoTracks = stream.getVideoTracks();
      if (videoTracks.length === 0 || !videoTracks[0].enabled) {
        setErrorMessage('Video stream not active. Please refresh the page.');
        return;
      }

      // Check if MediaRecorder is supported
      if (!window.MediaRecorder) {
        setErrorMessage('Recording not supported by your browser. Please use Chrome, Firefox, or Safari.');
        return;
      }

      // Initialize background canvas for LipNet preprocessing
      const lipnetCanvas = initializeLipNetCanvas();

      // Prepare recording chunks for both streams
      const recordedChunks = [];
      const lipnetRecordedChunks = [];
      setRecordedChunks(recordedChunks);
      setLipnetRecordedChunks(lipnetRecordedChunks);

      // Create original video MediaRecorder with fallback options
      let mediaRecorder;
      try {
        // First try with VP9 codec
        mediaRecorder = new MediaRecorder(webcamRef.current.stream, {
          mimeType: 'video/webm;codecs=vp9',
          videoBitsPerSecond: 2500000
        });
      } catch (vp9Error) {
        console.warn('VP9 codec not supported, trying VP8:', vp9Error);
        try {
          // Fallback to VP8
          mediaRecorder = new MediaRecorder(webcamRef.current.stream, {
            mimeType: 'video/webm;codecs=vp8',
            videoBitsPerSecond: 2500000
          });
        } catch (vp8Error) {
          console.warn('VP8 codec not supported, using default:', vp8Error);
          // Final fallback to default
          mediaRecorder = new MediaRecorder(webcamRef.current.stream);
        }
      }

      mediaRecorderRef.current = mediaRecorder;

      // Create LipNet preprocessed video MediaRecorder with codec optimization
      let lipnetMediaRecorder = null;
      try {
        // Start frame processing FIRST to populate the canvas
        setLipnetProcessingActive(true);
        frameProcessingRef.current = requestAnimationFrame(processFrameForLipNet);

        // Wait a brief moment for the canvas to have content, then create stream
        await new Promise(resolve => setTimeout(resolve, 200));

        // Create stream from the processed LipNet canvas (this will contain cropped, grayscale frames)
        const lipnetStream = lipnetCanvas.captureStream(REALTIME_LIPNET_OPTIONS.frameRate);
        console.log('🎨 LipNet stream: canvas capture (processed frames)');

        // Codec priority chain for optimal LipNet compatibility
        const codecOptions = [
          { mimeType: 'video/mp4; codecs="avc1.42E01E"', name: 'H.264 (MP4)' },
          { mimeType: 'video/webm;codecs=vp9', name: 'VP9 (WebM)' },
          { mimeType: 'video/webm;codecs=vp8', name: 'VP8 (WebM)' },
          { mimeType: 'video/webm', name: 'WebM (default)' }
        ];

        let selectedCodec = null;
        for (const codec of codecOptions) {
          if (MediaRecorder.isTypeSupported(codec.mimeType)) {
            try {
              lipnetMediaRecorder = new MediaRecorder(lipnetStream, {
                mimeType: codec.mimeType,
                videoBitsPerSecond: 2_000_000 // Optimized 2 Mbps for LipNet
              });
              selectedCodec = codec;
              console.log(`🎨 LipNet codec selected: ${codec.name}`);
              break;
            } catch (codecError) {
              console.warn(`⚠️ Failed to create LipNet MediaRecorder with ${codec.name}:`, codecError);
              continue;
            }
          } else {
            console.warn(`⚠️ LipNet codec not supported: ${codec.name}`);
          }
        }

        // Fallback to browser default if all specific codecs fail
        if (!lipnetMediaRecorder) {
          console.warn('⚠️ All preferred codecs failed, using browser default for LipNet');
          lipnetMediaRecorder = new MediaRecorder(lipnetStream, {
            videoBitsPerSecond: 2_000_000
          });
          selectedCodec = { name: 'Browser Default' };
          console.log('🎨 LipNet codec selected: Browser Default');
        }

        lipnetMediaRecorderRef.current = lipnetMediaRecorder;
        console.log(`🎨 LipNet MediaRecorder created successfully with ${selectedCodec.name}`);
        console.log('🎨 LipNet bitrate: 2 Mbps, Original bitrate: 2.5 Mbps');

        // Store codec info for quality metrics
        setLipnetQualityMetrics(prev => ({
          ...prev,
          codecUsed: selectedCodec.name
        }));
      } catch (lipnetError) {
        console.warn('⚠️ LipNet MediaRecorder creation failed, will use original video only:', lipnetError);
        lipnetMediaRecorderRef.current = null;
      }

      // Setup data collection for original video
      mediaRecorder.ondataavailable = (event) => {
        if (event.data && event.data.size > 0) {
          recordedChunks.push(event.data);
        }
      };

      // Setup data collection for LipNet preprocessed video
      if (lipnetMediaRecorder) {
        lipnetMediaRecorder.ondataavailable = (event) => {
          if (event.data && event.data.size > 0) {
            lipnetRecordedChunks.push(event.data);
          }
        };
      }

      // Handle completion of both recordings
      let originalVideoCompleted = false;
      let lipnetVideoCompleted = false;
      let originalVideoBlob = null;
      let lipnetVideoBlob = null;

      const checkBothRecordingsComplete = async () => {
        if (originalVideoCompleted && (lipnetVideoCompleted || !lipnetMediaRecorder)) {
          console.log('🎬 Both recordings completed, processing...');

          // Apply oval cropping to original video for privacy compliance
          let processedOriginalBlob = originalVideoBlob;
          try {
            console.log('  🔄 Applying oval cropping for privacy compliance...');
            const ovalCropArea = {
              x: (VIDEO_CONSTRAINTS.width - 400) / 2,
              y: (VIDEO_CONSTRAINTS.height - 500) / 2,
              width: 400,
              height: 500
            };

            processedOriginalBlob = await createOvalCroppedVideo(originalVideoBlob, ovalCropArea, {
              targetWidth: 400,
              targetHeight: 500,
              quality: 0.8,
              frameRate: 25
            });
            console.log('  ✅ Oval cropping completed');
          } catch (error) {
            console.warn('  ⚠️ Oval cropping failed, using original video:', error);
            processedOriginalBlob = originalVideoBlob;
          }

          console.log('  🚀 Calling handleRecordingComplete with dual videos...');
          await handleRecordingComplete(processedOriginalBlob, lipnetVideoBlob);
          console.log('  ✅ handleRecordingComplete completed');
        }
      };

      mediaRecorder.onstop = async () => {
        console.log('🎬 Original MediaRecorder onstop event triggered');
        console.log('  recordedChunks length:', recordedChunks.length);

        originalVideoBlob = new Blob(recordedChunks, { type: 'video/webm' });
        console.log('  originalVideoBlob created:', { size: originalVideoBlob.size, type: originalVideoBlob.type });

        originalVideoCompleted = true;
        await checkBothRecordingsComplete();
      };

      // Setup LipNet recording completion handler
      if (lipnetMediaRecorder) {
        lipnetMediaRecorder.onstop = async () => {
          console.log('🎨 LipNet MediaRecorder onstop event triggered');
          console.log('  lipnetRecordedChunks length:', lipnetRecordedChunks.length);

          if (lipnetRecordedChunks.length > 0) {
            lipnetVideoBlob = new Blob(lipnetRecordedChunks, { type: 'video/webm' });
            console.log('  lipnetVideoBlob created:', { size: lipnetVideoBlob.size, type: lipnetVideoBlob.type });

            // Generate processing summary
            console.log(`🎨 LipNet Summary: ${lipnetQualityMetrics.frameCount} frames, ${lipnetQualityMetrics.avgConfidence.toFixed(2)} avg confidence, ${lipnetQualityMetrics.codecUsed} format`);
            if (lipnetQualityMetrics.processingErrors > 0) {
              console.warn(`🎨 Processing errors encountered: ${lipnetQualityMetrics.processingErrors}`);
            }
          } else {
            console.warn('  ⚠️ No LipNet chunks recorded, will use original video only');
            lipnetVideoBlob = null;
          }

          lipnetVideoCompleted = true;
          await checkBothRecordingsComplete();
        };
      }

      setIsRecording(true);
      setRecordingTimer(5);

      // Reset quality metrics for new recording
      setLipnetQualityMetrics({
        frameCount: 0,
        avgConfidence: 0,
        codecUsed: lipnetMediaRecorder ? 'Initialized' : 'None',
        processingErrors: 0
      });

      // Start both recordings simultaneously
      mediaRecorder.start();
      if (lipnetMediaRecorder) {
        lipnetMediaRecorder.start();
        console.log('🎨 LipNet recording started');
      }

      // Frame processing already started earlier to populate canvas before MediaRecorder creation

      // 5-second countdown timer
      let countdown = 5;
      const timerInterval = setInterval(() => {
        countdown--;
        setRecordingTimer(countdown);

        if (countdown <= 0) {
          clearInterval(timerInterval);

          // Stop both recordings
          if (mediaRecorder.state === 'recording') {
            mediaRecorder.stop();
          }
          if (lipnetMediaRecorder && lipnetMediaRecorder.state === 'recording') {
            lipnetMediaRecorder.stop();
          }

          // Stop frame processing
          setLipnetProcessingActive(false);
          if (frameProcessingRef.current) {
            cancelAnimationFrame(frameProcessingRef.current);
            frameProcessingRef.current = null;
          }

          setRecordingTimer(0);
        }
      }, 1000);

      // Stop recording after 5 seconds (backup)
      setTimeout(() => {
        clearInterval(timerInterval);

        // Stop both recordings
        if (mediaRecorder.state === 'recording') {
          mediaRecorder.stop();
        }
        if (lipnetMediaRecorder && lipnetMediaRecorder.state === 'recording') {
          lipnetMediaRecorder.stop();
        }

        // Stop frame processing
        setLipnetProcessingActive(false);
        if (frameProcessingRef.current) {
          cancelAnimationFrame(frameProcessingRef.current);
          frameProcessingRef.current = null;
        }

        setRecordingTimer(0);
      }, 5000);

    } catch (error) {
      console.error('=== ERROR DURING RECORDING START ===');
      console.error('Error details:', error);
      console.error('Error stack:', error.stack);

      // Provide more specific error messages for recording start failures
      let errorMessage = 'Failed to start recording';
      if (error.message) {
        if (error.message.includes('camera') || error.message.includes('video')) {
          errorMessage = 'Camera access error. Please check camera permissions and try again.';
        } else if (error.message.includes('MediaRecorder')) {
          errorMessage = 'Recording not supported by your browser. Please try a different browser.';
        } else if (error.message.includes('stream')) {
          errorMessage = 'Video stream error. Please refresh the page and try again.';
        } else {
          errorMessage = `Recording start failed: ${error.message}`;
        }
      }

      setErrorMessage(errorMessage);
      setIsRecording(false);
      setRecordingTimer(0);
    }
  }, [mouthPosition, handleRecordingComplete, initializeLipNetCanvas, processFrameForLipNet]);

  // Initialize camera and face detection model when component mounts
  useEffect(() => {
    const initModel = async () => {
      try {
        console.log('Initializing TensorFlow.js...');
        await tf.ready();
        console.log('TensorFlow.js ready');

        console.log('Loading MediaPipe FaceMesh model...');
        const model = faceLandmarksDetection.SupportedModels.MediaPipeFaceMesh;
        const detectorConfig = {
          runtime: 'mediapipe',
          solutionPath: 'https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh',
          refineLandmarks: false,
        };

        const loadedModel = await faceLandmarksDetection.createDetector(model, detectorConfig);
        console.log('MediaPipe FaceMesh model loaded successfully');
        setModel(loadedModel);
      } catch (error) {
        console.error('Error initializing face detection model:', error);
        const errorInfo = handleError(error, 'Face Detection Initialization');
        setErrorMessage(`Face detection unavailable: ${errorInfo.message}`);
        // Continue without face detection
        setModel(null);
      }
    };

    checkCameraPermissions();
    initModel();

    return () => {
      if (detectionRef.current) {
        cancelAnimationFrame(detectionRef.current);
      }
      if (frameProcessingRef.current) {
        cancelAnimationFrame(frameProcessingRef.current);
      }
      cleanupLipNetCanvas();
    };
  }, [checkCameraPermissions]);

  // Start face detection when model is loaded
  useEffect(() => {
    if (permissionGranted) {
      // Start detection loop regardless of model availability
      detectFace();

      // Also start LipNet processing for debugging (temporarily)
      console.log('🎯 Starting LipNet processing for debugging...');
      initializeLipNetCanvas();
      setLipnetProcessingActive(true);
      setTimeout(() => {
        frameProcessingRef.current = requestAnimationFrame(processFrameForLipNet);
      }, 1000); // Wait 1 second for camera to be ready
    }

    return () => {
      if (detectionRef.current) {
        cancelAnimationFrame(detectionRef.current);
      }
      if (frameProcessingRef.current) {
        cancelAnimationFrame(frameProcessingRef.current);
      }
    };
  }, [model, permissionGranted, detectFace, initializeLipNetCanvas, processFrameForLipNet]);

  // Test AWS connection on component mount
  useEffect(() => {
    const testConnection = async () => {
      console.log('🧪 Testing AWS connection on VideoRecorder mount...');

      try {
        const result = await testAWSConnection();
        if (result.success) {
          console.log('✅ AWS connection test passed:', result);
          console.log('🔧 AWS Configuration Status:');
          console.log('  - Identity Pool ID configured:', !!process.env.REACT_APP_AWS_IDENTITY_POOL_ID);
          console.log('  - Region configured:', process.env.REACT_APP_AWS_REGION);
          console.log('  - Bucket configured:', process.env.REACT_APP_S3_BUCKET);
        } else {
          console.warn('⚠️ AWS connection test failed:', result);
          console.warn('🔧 This may indicate AWS credentials or permissions issues');
        }
      } catch (error) {
        console.error('❌ AWS connection test error:', error);
        console.error('🔧 Check AWS configuration in .env file');
      }
    };

    testConnection();
  }, []);

  // Removed complex synchronization logic - using direct parent data

  // Removed all complex debugging and synchronization logic

  // Track phrase changes for debugging
  useEffect(() => {
    console.log('📝 VideoRecorder: Phrase prop changed to:', phrase);
    console.log('📝 VideoRecorder: Category prop changed to:', category);
    console.log('📝 VideoRecorder: BLACK OVERLAY TEXT should now display:', phrase);
    console.log('📝 VideoRecorder: DOM should update immediately with new phrase text');

    // Force a re-render to ensure phrase text updates
    if (phrase) {
      console.log('📝 VideoRecorder: Phrase text confirmed for overlay:', phrase);
    }
  }, [phrase, category]);



  // Webcam component
  return (
    <Box sx={{ position: 'relative', width: '100%', height: '100%', py: 4 }}>








      <Box
        sx={{
          position: 'relative',
          width: { xs: '300px', sm: '340px', md: '380px' },
          height: { xs: '420px', sm: '480px', md: '540px' },
          borderRadius: '50%',
          overflow: 'hidden',
          bgcolor: '#000',
          border: '4px solid #2196F3',
          margin: '0 auto',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          boxShadow: '0 8px 32px rgba(33, 150, 243, 0.3)',
          transform: 'scaleY(1.0)'
        }}
      >
        {permissionGranted && !cameraError ? (
          <>
            <Webcam
              ref={webcamRef}
              audio={false}
              videoConstraints={getVideoConstraints()}
              mirrored={false} // Ensure the underlying video stream is not mirrored
              style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                width: 'auto',
                height: '100%',
                minWidth: '100%',
                objectFit: 'cover',
                // Mirror the preview for user comfort but keep the actual stream non-mirrored
                transform: `translate(-50%, -50%) scale(${zoomLevel}) scaleX(-1)`,
                transformOrigin: 'center center'
              }}
              onLoadedMetadata={() => {
                if (webcamRef.current && webcamRef.current.video) {
                  const video = webcamRef.current.video;
                  if (video.videoWidth > 0 && video.videoHeight > 0) {
                    updateCameraResolution(video);
                  } else {
                    console.warn('Video metadata loaded but dimensions not yet available');
                  }
                }
              }}
            />
            <canvas
              ref={canvasRef}
              style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                width: '120%',
                height: '120%',
                // Mirror the canvas overlay to match the mirrored preview
                transform: 'translate(-50%, -50%) scaleX(-1)',
                pointerEvents: 'none'
              }}
              width={VIDEO_CONSTRAINTS.width}
              height={VIDEO_CONSTRAINTS.height}
            />

            {/* Semi-transparent lip guide overlay for optimal mouth positioning */}
            {permissionGranted && !isRecording && (
              <img
                src="/images/lips-no-bg.png"
                alt="Lip positioning guide"
                style={{
                  position: 'absolute',
                  top: '75%', // Position in lower half of oval viewport
                  left: '50%',
                  transform: 'translate(-50%, -50%) scaleX(-1)', // Mirror to match video preview and center
                  width: '41%', // 25% larger than original 33%
                  height: 'auto', // Maintain aspect ratio
                  opacity: 0.25, // 25% opacity for subtle transparency
                  pointerEvents: 'none', // Non-interactive
                  zIndex: 3, // Above video and canvas, below text overlays (zIndex 5)
                  transition: 'opacity 0.3s ease-in-out', // Smooth fade transitions
                  objectFit: 'contain', // Preserve aspect ratio
                  maxWidth: '150px', // 25% larger than original 120px
                  filter: 'brightness(1.2) contrast(1.1)' // Enhance visibility through semi-transparency
                }}
              />
            )}

            {/* Black overlay with phrase text inside oval viewport */}
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '50%',
                background: 'rgba(0,0,0,1.0)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                zIndex: 5,
                pointerEvents: 'none'
              }}
            >
              <Typography
                key={phrase} // Force re-render when phrase changes
                variant="h5"
                sx={{
                  color: 'white',
                  fontWeight: 'bold',
                  textAlign: 'center',
                  textShadow: '2px 2px 4px rgba(0,0,0,0.8)',
                  fontSize: { xs: '1.2rem', sm: '1.4rem', md: '1.6rem' },
                  px: 2,
                  lineHeight: 1.2,
                  // Add transition for smooth phrase changes
                  transition: 'opacity 0.3s ease-in-out',
                  opacity: phrase ? 1 : 0.7
                }}
              >
                {phrase || 'Loading phrase...'}
              </Typography>
            </Box>
          </>
        ) : (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              bgcolor: '#333',
              color: 'white'
            }}
          >
            <Typography>Camera Loading...</Typography>
          </Box>
        )}



        {/* Controls container - positioned below oval with proper spacing - only show when camera is active */}
        {permissionGranted && !cameraError && (
        <Box
          sx={{
            position: 'absolute',
            bottom: { xs: -180, sm: -170, md: -160 },
            left: '50%',
            transform: 'translateX(-50%)',
            width: '100%',
            maxWidth: { xs: 350, sm: 400, md: 450 },
            bgcolor: 'rgba(0, 0, 0, 0.9)', // Increased opacity for better visibility
            borderRadius: 2,
            p: 3, // Increased padding for better visibility
            zIndex: 10,
            border: '1px solid rgba(255, 255, 255, 0.2)', // Add subtle border for visibility
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.5)' // Add shadow for better visibility
          }}
        >
          {/* Camera selection */}
          {availableCameras.length > 1 && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" sx={{ color: 'white', mb: 1 }}>
                Select Camera:
              </Typography>
              <select
                value={selectedCamera}
                onChange={(e) => setSelectedCamera(e.target.value)}
                style={{
                  width: '100%',
                  padding: '8px',
                  borderRadius: '4px',
                  border: 'none',
                  fontSize: '14px'
                }}
              >
                {availableCameras.map((camera) => (
                  <option key={camera.deviceId} value={camera.deviceId}>
                    {camera.label || `Camera ${camera.deviceId.slice(0, 8)}`}
                  </option>
                ))}
              </select>
            </Box>
          )}

          {/* Recording status and mouth tracking */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <FiberManualRecordIcon
                sx={{
                  color: isRecording ? 'error.main' : 'success.main',
                  fontSize: 12
                }}
              />
              <Typography variant="body2" sx={{ color: 'white' }}>
                {isRecording ? 'Recording...' : 'Ready'}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <CircleIcon
                sx={{
                  color: model ? (mouthTrackingQuality > 0.7 ? 'success.main' : 'warning.main') : 'info.main',
                  fontSize: 12
                }}
              />
              <Typography variant="body2" sx={{ color: 'white' }}>
                {!model ? 'Manual Mode' : (mouthTrackingQuality > 0.7 ? 'Good' : 'Adjust Position')}
              </Typography>
            </Box>
          </Box>


        </Box>
        )}
      </Box>

      {/* Controls row: Zoom slider below oval, completed dots to the right */}
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mt: 2, mb: 2 }}>
        {/* Zoom control directly under oval */}
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            maxWidth: 400
          }}
        >
          <Typography variant="body2" sx={{ color: 'text.primary', mb: 1, fontWeight: 'bold' }}>
            Zoom: {zoomLevel.toFixed(1)}x
          </Typography>
          <Slider
            value={zoomLevel}
            min={1}
            max={3}
            step={0.1}
            onChange={(_, value) => setZoomLevel(value)}
            sx={{
              color: '#1976d2',
              height: 8,
              width: '90%',
              '& .MuiSlider-thumb': {
                bgcolor: '#1976d2',
                width: 20,
                height: 20,
                '&:hover': {
                  boxShadow: '0 0 0 8px rgba(25, 118, 210, 0.16)'
                }
              },
              '& .MuiSlider-track': {
                bgcolor: '#1976d2',
                height: 8
              },
              '& .MuiSlider-rail': {
                bgcolor: '#bdbdbd',
                height: 8
              }
            }}
          />
        </Box>
      </Box>

      {/* 5-Second Timer positioned to the right of oval */}
      {isRecording && (
        <Box
          sx={{
            position: 'absolute',
            right: { xs: -80, sm: -100, md: -120 },
            top: '50%',
            transform: 'translateY(-50%)',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            width: 80,
            height: 80,
            bgcolor: 'rgba(0, 0, 0, 0.8)',
            borderRadius: '50%',
            border: '3px solid #f44336',
            zIndex: 15
          }}
        >
          <Typography
            variant="h3"
            sx={{
              color: 'white',
              fontWeight: 'bold',
              fontSize: { xs: '2rem', sm: '2.5rem' },
              lineHeight: 1
            }}
          >
            {recordingTimer}
          </Typography>
          <Typography
            variant="caption"
            sx={{
              color: 'white',
              fontSize: '0.7rem',
              mt: 0.5
            }}
          >
            SEC
          </Typography>
        </Box>
      )}



      {cameraError && (
        <Box sx={{ mt: 2, p: 2, bgcolor: 'rgba(244, 67, 54, 0.1)', borderRadius: 2 }}>
          <Typography variant="body2" color="error" gutterBottom>
            <strong>Camera Error:</strong> {errorMessage}
          </Typography>
          {isMobile && (
            <Box sx={{ mt: 2, p: 2, bgcolor: 'rgba(0,150,136,0.1)', borderRadius: 2 }}>
              <Typography variant="body2" align="left">
                <strong>Mobile Tips:</strong>
                <ul style={{ paddingLeft: '20px', textAlign: 'left' }}>
                  <li>Make sure to allow camera permissions when prompted</li>
                  <li>On iOS, Safari is recommended as it may allow camera access on local networks</li>
                  <li>For iOS: Go to Settings &gt; Safari &gt; Camera &gt; Allow for this website</li>
                  <li>For Android: Try using Chrome and ensure all permissions are granted</li>
                  <li>Refresh the page after granting permissions</li>
                </ul>
              </Typography>
            </Box>
          )}
          <Button
            variant="contained"
            color="primary"
            onClick={() => window.location.reload()}
            sx={{ mt: 3 }}
          >
            Refresh Page
          </Button>
        </Box>
      )}

      {!permissionGranted && !cameraError && (
        <WebcamPermissionHandler 
          onPermissionGranted={() => setPermissionGranted(true)}
          onError={(msg) => {
            setErrorMessage(msg);
            setCameraError(true);
          }}
        />
      )}
      

      
      {/* Main controls container with recording buttons centered and exit button positioned right */}
      <Box sx={{ mt: 2, position: 'relative', display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 48 }}>
        {/* Primary recording controls - centered */}
        <Box sx={{ display: 'flex', gap: 2 }}>
          {!isRecording ? (
            <>
              <Button
                variant="contained"
                color="primary"
                onClick={handleStartRecording}
                disabled={disabled || cameraError || processing}
                sx={{
                  minWidth: 120,
                  opacity: processing ? 0.6 : 1
                }}
              >
                {processing
                  ? (processingPhase === 'uploading' ? 'Uploading...' : 'Processing...')
                  : 'Start Recording'
                }
              </Button>

              {canReRecord && (
                <Button
                  variant="outlined"
                  color="secondary"
                  onClick={() => {
                    console.log('Record Again clicked - starting new recording');
                    // Don't reset recording count - let parent manage it
                    handleStartRecording();
                  }}
                  disabled={disabled || cameraError || processing}
                  sx={{
                    minWidth: 120,
                    opacity: processing ? 0.6 : 1
                  }}
                >
                  {processing
                    ? (processingPhase === 'uploading' ? 'Uploading...' : 'Processing...')
                    : 'Record Again'
                  }
                </Button>
              )}
            </>
          ) : (
            <Button
              variant="contained"
              color="error"
              onClick={handleStopRecording}
              disabled={processing}
              sx={{
                minWidth: 120,
                opacity: processing ? 0.6 : 1
              }}
            >
              {processing ? 'Processing...' : 'Stop Recording'}
            </Button>
          )}
        </Box>

        {/* Early exit button removed */}
      </Box>
      
      {processing && (
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1, mt: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <CircularProgress size={24} />
            <Typography>
              {processingPhase === 'uploading'
                ? 'Uploading to AWS S3...'
                : 'Processing video...'
              }
            </Typography>
          </Box>
          {processingPhase === 'uploading' && uploadProgress > 0 && (
            <Box sx={{ width: '100%', maxWidth: 200 }}>
              <Typography variant="caption" sx={{ color: 'text.secondary', mb: 0.5 }}>
                Upload Progress: {uploadProgress}%
              </Typography>
              <LinearProgress
                variant="determinate"
                value={uploadProgress}
                sx={{ height: 4, borderRadius: 2 }}
              />
            </Box>
          )}
          <Typography variant="caption" sx={{ color: 'text.secondary', textAlign: 'center' }}>
            Please wait - recording buttons are disabled during upload
          </Typography>
        </Box>
      )}
      
      {errorMessage && (
        <Alert severity="error" sx={{ mt: 2 }}>
          {errorMessage}
        </Alert>
      )}

      {/* Upload mode indicator */}
      {isBackendUploadMode && !isDevelopmentMode && (
        <Alert severity="success" sx={{ mt: 2 }}>
          <strong>Backend Upload Mode:</strong> Videos are being uploaded to AWS S3 via secure backend server.
        </Alert>
      )}

      {/* Development mode indicator */}
      {isDevelopmentMode && (
        <Alert severity="warning" sx={{ mt: 2 }}>
          <strong>Development Mode:</strong> Backend server not configured. Recordings will be simulated locally.
        </Alert>
      )}



      {/* Early exit dialog removed */}

      <Snackbar
        open={showSavedNotification}
        autoHideDuration={3000}
        onClose={() => setShowSavedNotification(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert severity="success" variant="filled">
          Recordings saved successfully!
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default VideoRecorder;
